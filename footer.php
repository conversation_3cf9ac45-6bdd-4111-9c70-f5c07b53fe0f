    <!-- End of main content -->

    <footer class="modern-footer">
        <div class="footer-content">
            <div class="container">
                <div class="row">
                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-section">
                            <h5 class="footer-title">
                                <i class="fas fa-id-card-alt me-2"></i>
                                Pakistan Identity System
                            </h5>
                            <p class="footer-description">
                                Secure and efficient digital identity card registration and management system
                                for the Islamic Republic of Pakistan, powered by modern technology.
                            </p>
                            <div class="footer-badges">
                                <span class="badge bg-success me-2">
                                    <i class="fas fa-shield-alt me-1"></i>Secure
                                </span>
                                <span class="badge bg-primary me-2">
                                    <i class="fas fa-bolt me-1"></i>Fast
                                </span>
                                <span class="badge bg-info">
                                    <i class="fas fa-mobile-alt me-1"></i>Mobile Ready
                                </span>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-section">
                            <h6 class="footer-subtitle">Quick Links</h6>
                            <ul class="footer-links">
                                <li><a href="index.php"><i class="fas fa-file-plus me-2"></i>New Application</a></li>
                                <li><a href="show_data.php"><i class="fas fa-database me-2"></i>View Records</a></li>
                                <li><a href="track.php"><i class="fas fa-search-location me-2"></i>Track Status</a></li>
                                <li><a href="slip.php"><i class="fas fa-receipt me-2"></i>Generate Slip</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-2 col-md-6 mb-4">
                        <div class="footer-section">
                            <h6 class="footer-subtitle">Management</h6>
                            <ul class="footer-links">
                                <li><a href="submit_email.php"><i class="fas fa-user-plus me-2"></i>Add Verifier</a></li>
                                <li><a href="show_emails.php"><i class="fas fa-users me-2"></i>View Verifiers</a></li>
                                <li><a href="email_list.php"><i class="fas fa-envelope me-2"></i>Email List</a></li>
                            </ul>
                        </div>
                    </div>

                    <div class="col-lg-4 col-md-6 mb-4">
                        <div class="footer-section">
                            <h6 class="footer-subtitle">System Information</h6>
                            <div class="footer-info">
                                <div class="info-item">
                                    <i class="fas fa-server text-success me-2"></i>
                                    <span>System Status: <strong class="text-success">Online</strong></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-clock text-primary me-2"></i>
                                    <span>Last Updated: <strong><?php echo date('M d, Y'); ?></strong></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-code text-info me-2"></i>
                                    <span>Version: <strong>2.0.0</strong></span>
                                </div>
                                <div class="info-item">
                                    <i class="fas fa-shield-alt text-warning me-2"></i>
                                    <span>Security: <strong class="text-success">SSL Encrypted</strong></span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <hr class="footer-divider">

                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="footer-copyright">
                            <p class="mb-0">
                                <i class="fas fa-copyright me-1"></i>
                                <?php echo date('Y'); ?> Document Management System. All rights reserved.
                            </p>
                            <small class="text-muted">
                                Developed for Document Processing Services
                            </small>
                        </div>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="footer-tech">
                            <small class="text-muted">
                                Powered by <i class="fab fa-php text-primary"></i> PHP,
                                <i class="fab fa-bootstrap text-primary"></i> Bootstrap &
                                <i class="fas fa-database text-success"></i> MySQL
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </footer>

    <style>
        .modern-footer {
            background: linear-gradient(135deg, #2c5530 0%, #1a3a1e 100%);
            color: white;
            margin-top: 50px;
            position: relative;
            overflow: hidden;
        }

        .modern-footer::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="footerGrain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.05"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.05"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.05"/></pattern></defs><rect width="100" height="100" fill="url(%23footerGrain)"/></svg>');
            pointer-events: none;
        }

        .footer-content {
            padding: 50px 0 30px;
            position: relative;
            z-index: 1;
        }

        .footer-section {
            height: 100%;
        }

        .footer-title {
            color: #ffd700;
            font-weight: 700;
            font-size: 1.3rem;
            margin-bottom: 20px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .footer-subtitle {
            color: #e8f5e8;
            font-weight: 600;
            font-size: 1.1rem;
            margin-bottom: 15px;
            border-bottom: 2px solid rgba(255, 215, 0, 0.3);
            padding-bottom: 8px;
        }

        .footer-description {
            color: #b8d4ba;
            line-height: 1.6;
            margin-bottom: 20px;
        }

        .footer-badges .badge {
            font-size: 0.75rem;
            padding: 5px 10px;
            border-radius: 15px;
        }

        .footer-links {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .footer-links li {
            margin-bottom: 10px;
        }

        .footer-links a {
            color: #b8d4ba;
            text-decoration: none;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            padding: 5px 0;
            border-radius: 5px;
        }

        .footer-links a:hover {
            color: #ffd700;
            transform: translateX(5px);
            background: rgba(255, 255, 255, 0.05);
            padding-left: 10px;
        }

        .footer-info .info-item {
            display: flex;
            align-items: center;
            margin-bottom: 12px;
            color: #b8d4ba;
            font-size: 0.9rem;
        }

        .footer-divider {
            border-color: rgba(255, 215, 0, 0.3);
            margin: 30px 0 20px;
        }

        .footer-copyright {
            color: #b8d4ba;
        }

        .footer-tech {
            color: #b8d4ba;
        }

        .footer-tech i {
            font-size: 1.1rem;
            margin: 0 2px;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .footer-content {
                padding: 30px 0 20px;
            }

            .footer-title {
                font-size: 1.1rem;
            }

            .footer-subtitle {
                font-size: 1rem;
            }

            .col-md-6.text-md-end {
                text-align: center !important;
                margin-top: 15px;
            }
        }

        /* Animation */
        .footer-section {
            animation: fadeInUp 0.6s ease forwards;
            opacity: 0;
            transform: translateY(30px);
        }

        .footer-section:nth-child(1) { animation-delay: 0.1s; }
        .footer-section:nth-child(2) { animation-delay: 0.2s; }
        .footer-section:nth-child(3) { animation-delay: 0.3s; }
        .footer-section:nth-child(4) { animation-delay: 0.4s; }

        @keyframes fadeInUp {
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
    </style>

    <!-- Additional Scripts -->
    <script>
        // Footer animations and interactions
        $(document).ready(function() {
            // Add smooth scrolling to footer links
            $('.footer-links a').on('click', function(e) {
                const href = $(this).attr('href');
                if (href.startsWith('#')) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: $(href).offset().top - 100
                    }, 800);
                }
            });

            // Add hover effects to info items
            $('.info-item').hover(
                function() { $(this).addClass('text-white'); },
                function() { $(this).removeClass('text-white'); }
            );

            // Update system status indicator
            setInterval(function() {
                const statusElement = $('.info-item:first strong');
                statusElement.fadeOut(200).fadeIn(200);
            }, 5000);
        });
    </script>

</body>
</html>
