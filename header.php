<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document Management System</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <style>
        :root {
            --primary-color: #2c5530;
            --secondary-color: #4a7c59;
            --accent-color: #f8f9fa;
            --text-dark: #2d3436;
            --border-color: #e9ecef;
            --success-color: #28a745;
            --warning-color: #ffc107;
            --danger-color: #dc3545;
            --light-bg: #f5f7fa;
        }

        body {
            font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            color: var(--text-dark);
            line-height: 1.6;
            min-height: 100vh;
        }

        .navbar {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            box-shadow: 0 10px 30px rgba(0,0,0,0.15);
            padding: 1rem 0;
            position: relative;
            backdrop-filter: blur(10px);
        }

        .navbar::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
            pointer-events: none;
        }

        .navbar-brand {
            font-weight: 700;
            color: white !important;
            font-size: 1.8rem;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            position: relative;
            z-index: 1;
            transition: all 0.3s ease;
        }

        .navbar-brand:hover {
            color: #f8f9fa !important;
            transform: scale(1.05);
        }

        .navbar-brand i {
            margin-right: 10px;
            color: #ffd700;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }

        .nav-link {
            color: white !important;
            font-weight: 500;
            padding: 0.7rem 1.2rem;
            margin: 0 0.2rem;
            border-radius: 12px;
            transition: all 0.3s ease;
            position: relative;
            z-index: 1;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }

        .nav-link:hover {
            color: white !important;
            background: rgba(255, 255, 255, 0.2);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-link.active {
            color: white !important;
            background: rgba(255, 255, 255, 0.25);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .nav-link i {
            margin-right: 6px;
            font-size: 0.9rem;
        }

        .navbar-toggler {
            border: 2px solid rgba(255, 255, 255, 0.3);
            padding: 0.5rem;
            border-radius: 8px;
            position: relative;
            z-index: 1;
        }

        .navbar-toggler:focus {
            box-shadow: 0 0 0 0.2rem rgba(255, 255, 255, 0.25);
            outline: none;
        }

        .navbar-toggler i {
            color: white;
            font-size: 1.2rem;
        }

        .logout-btn {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c0392b 100%) !important;
            color: white !important;
            padding: 0.7rem 1.5rem !important;
            border-radius: 12px;
            transition: all 0.3s ease;
            border: 2px solid rgba(255, 255, 255, 0.2);
            font-weight: 600;
        }

        .logout-btn:hover {
            background: linear-gradient(135deg, #c0392b 0%, #a93226 100%) !important;
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
            color: white !important;
        }

        .container {
            max-width: 1400px;
            padding: 0 1rem;
        }

        /* Card Styles */
        .card {
            border-radius: 20px;
            border: none;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            background: white;
        }

        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
        }

        /* Button Styles */
        .btn {
            border-radius: 12px;
            padding: 0.7rem 1.8rem;
            font-weight: 600;
            transition: all 0.3s ease;
            border: none;
        }

        .btn-primary {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
        }

        .btn-primary:hover {
            background: linear-gradient(135deg, var(--secondary-color) 0%, var(--primary-color) 100%);
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(44, 85, 48, 0.3);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, var(--success-color) 0%, #218838 100%);
            color: white;
        }

        .btn-success:hover {
            background: linear-gradient(135deg, #218838 0%, #1e7e34 100%);
            transform: translateY(-2px);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, var(--danger-color) 0%, #c82333 100%);
            color: white;
        }

        .btn-danger:hover {
            background: linear-gradient(135deg, #c82333 0%, #bd2130 100%);
            transform: translateY(-2px);
            color: white;
        }

        /* Form Controls */
        .form-control, .form-select {
            border-radius: 12px;
            border: 2px solid var(--border-color);
            padding: 0.75rem 1rem;
            transition: all 0.3s ease;
            background: white;
        }

        .form-control:focus, .form-select:focus {
            border-color: var(--primary-color);
            box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
            outline: none;
        }

        /* Table Styles */
        .table {
            border-radius: 15px;
            overflow: hidden;
            background: white;
        }

        .table thead th {
            background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
            color: white;
            font-weight: 600;
            border: none;
            padding: 15px 10px;
        }

        .table tbody tr:hover {
            background-color: rgba(44, 85, 48, 0.05);
        }

        /* Utilities */
        .shadow-sm {
            box-shadow: 0 5px 15px rgba(0,0,0,0.08) !important;
        }

        .shadow-md {
            box-shadow: 0 10px 25px rgba(0,0,0,0.1) !important;
        }

        .shadow-lg {
            box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .navbar-brand {
                font-size: 1.5rem;
            }

            .nav-link {
                padding: 0.5rem 1rem;
                margin: 0.2rem 0;
            }

            .navbar-collapse {
                background: rgba(255, 255, 255, 0.1);
                border-radius: 12px;
                margin-top: 1rem;
                padding: 1rem;
                backdrop-filter: blur(10px);
            }
        }

        /* Animation */
        .navbar {
            animation: slideDown 0.6s ease forwards;
        }

        @keyframes slideDown {
            from {
                transform: translateY(-100%);
                opacity: 0;
            }
            to {
                transform: translateY(0);
                opacity: 1;
            }
        }

        /* Active page indicator */
        .nav-link.active::after {
            content: '';
            position: absolute;
            bottom: -5px;
            left: 50%;
            transform: translateX(-50%);
            width: 30px;
            height: 3px;
            background: #ffd700;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <nav class="navbar navbar-expand-lg sticky-top">
        <div class="container">
            <a class="navbar-brand" href="index.php">
                <i class="fas fa-file-alt"></i>
                Document System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav" aria-controls="navbarNav" aria-expanded="false" aria-label="Toggle navigation">
                <i class="fas fa-bars"></i>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="index.php" title="Create New Application">
                            <i class="fas fa-file-plus"></i> New Application
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="show_data.php" title="View All Records">
                            <i class="fas fa-database"></i> Show Records
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="slip.php" title="Generate Slip">
                            <i class="fas fa-receipt"></i> Generate Slip
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="submit_email.php" title="Add New Verifier">
                            <i class="fas fa-user-plus"></i> Add Verifier
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="show_emails.php" title="View All Verifiers">
                            <i class="fas fa-users"></i> Verifiers
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="email_list.php" title="Email Management">
                            <i class="fas fa-envelope-open-text"></i> Email List
                        </a>
                    </li>

                    <li class="nav-item">
                        <a class="nav-link logout-btn" href="logout.php" title="Logout from System">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <script>
        // Add active class to current page
        $(document).ready(function() {
            const currentPage = window.location.pathname.split('/').pop();
            $('.nav-link').each(function() {
                const href = $(this).attr('href');
                if (href === currentPage || (currentPage === '' && href === 'index.php')) {
                    $(this).addClass('active');
                }
            });

            // Add smooth hover effects
            $('.nav-link').hover(
                function() { $(this).addClass('shadow-sm'); },
                function() { $(this).removeClass('shadow-sm'); }
            );
        });
    </script>

    <!-- Start of main content -->
