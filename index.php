<?php
include('session.php');
include 'header.php';
include_once 'db_connection.php';

// Fetch application types from database
$application_types = [];
try {
    $sql_app_types = "SELECT urdu_name, english_name FROM application_types WHERE is_active = 1 ORDER BY english_name";
    $result_app_types = $conn->query($sql_app_types);
    if ($result_app_types && $result_app_types->num_rows > 0) {
        while ($row = $result_app_types->fetch_assoc()) {
            $application_types[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching application types: " . $e->getMessage());
}

// Fetch priorities from database
$priorities = [];
try {
    $sql_priorities = "SELECT urdu_name, english_name FROM priorities WHERE is_active = 1 ORDER BY id";
    $result_priorities = $conn->query($sql_priorities);
    if ($result_priorities && $result_priorities->num_rows > 0) {
        while ($row = $result_priorities->fetch_assoc()) {
            $priorities[] = $row;
        }
    }
} catch (Exception $e) {
    error_log("Error fetching priorities: " . $e->getMessage());
}

// If no data found in lookup tables, use fallback data
if (empty($application_types)) {
    $application_types = [
        ['urdu_name' => 'شناختی کارڈ میں ترمیم', 'english_name' => 'ID Card Modification'],
        ['urdu_name' => 'رینو', 'english_name' => 'Renewal'],
        ['urdu_name' => 'گمشدہ', 'english_name' => 'Lost Card'],
        ['urdu_name' => 'فیملی سرٹیفکیٹ', 'english_name' => 'Family Certificate'],
        ['urdu_name' => 'سمارٹ شناختی کارڈ', 'english_name' => 'Smart ID Card'],
        ['urdu_name' => 'شناختی کارڈمنسوخ', 'english_name' => 'ID Card Cancellation']
    ];
}

if (empty($priorities)) {
    $priorities = [
        ['urdu_name' => 'نارمل', 'english_name' => 'Normal'],
        ['urdu_name' => 'ارجنٹ', 'english_name' => 'Urgent'],
        ['urdu_name' => 'ایگزیکٹیو', 'english_name' => 'Executive']
    ];
}

$conn->close();
?>
<!DOCTYPE html>
<html lang="en">
<head>
  <title>Document Token Form</title>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <!-- Bootstrap 5 CSS -->
  <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
  <!-- Font Awesome -->
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <!-- Google Fonts -->
  <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
  <style>
    :root {
        --primary-color: #2c5530;
        --secondary-color: #4a7c59;
        --accent-color: #f8f9fa;
        --text-dark: #2d3436;
        --border-color: #e9ecef;
        --success-color: #28a745;
        --warning-color: #ffc107;
        --danger-color: #dc3545;
    }

    body {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
        min-height: 100vh;
        padding: 20px 0;
    }

    .main-container {
        max-width: 1000px;
        margin: 0 auto;
        background: white;
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        overflow: hidden;
    }

    .header-section {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
    }

    .header-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="white" opacity="0.1"/><circle cx="75" cy="75" r="1" fill="white" opacity="0.1"/><circle cx="50" cy="10" r="0.5" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    }

    .header-section h1 {
        font-size: 2.5rem;
        font-weight: 700;
        margin: 0;
        position: relative;
        z-index: 1;
    }

    .header-section .subtitle {
        font-size: 1.1rem;
        opacity: 0.9;
        margin-top: 10px;
        position: relative;
        z-index: 1;
    }

    .form-section {
        padding: 40px;
    }

    .section-card {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 25px;
        border-left: 4px solid var(--primary-color);
        box-shadow: 0 5px 15px rgba(0,0,0,0.05);
    }

    .section-title {
        color: var(--primary-color);
        font-weight: 700;
        margin-bottom: 20px;
        font-size: 1.3rem;
        display: flex;
        align-items: center;
        gap: 10px;
    }

    .form-group {
        margin-bottom: 20px;
    }

    .form-label {
        font-weight: 600;
        color: var(--text-dark);
        margin-bottom: 8px;
        font-size: 1rem;
        display: flex;
        align-items: center;
        gap: 8px;
    }

    .form-control, .form-select {
        border: 2px solid var(--border-color);
        border-radius: 12px;
        padding: 12px 15px;
        font-size: 1rem;
        transition: all 0.3s ease;
        background: white;
    }

    .form-control:focus, .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 0.2rem rgba(44, 85, 48, 0.25);
        outline: none;
    }

    .input-group {
        position: relative;
    }

    .input-group .form-control {
        padding-left: 45px;
    }

    .input-group-text {
        position: absolute;
        left: 15px;
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--secondary-color);
        z-index: 3;
        font-size: 1.1rem;
    }

    .btn-submit {
        background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%);
        border: none;
        color: white;
        padding: 15px 40px;
        font-size: 1.1rem;
        font-weight: 600;
        border-radius: 12px;
        transition: all 0.3s ease;
        width: 100%;
        position: relative;
        overflow: hidden;
    }

    .btn-submit:hover {
        transform: translateY(-2px);
        box-shadow: 0 10px 25px rgba(44, 85, 48, 0.3);
        color: white;
    }

    .btn-submit:active {
        transform: translateY(0);
    }

    .btn-submit::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
        transition: left 0.5s;
    }

    .btn-submit:hover::before {
        left: 100%;
    }

    .urdu-text {
        font-family: 'Jameel Noori Nastaleeq', 'Noto Nastaliq Urdu', serif;
        font-size: 16px;
        direction: rtl;
        text-align: right;
    }

    /* Image preview styling */
    #imagePreview {
        margin-top: 15px;
        text-align: center;
    }

    #imagePreview img {
        max-width: 200px;
        max-height: 200px;
        border-radius: 15px;
        box-shadow: 0 10px 25px rgba(0,0,0,0.1);
        display: none;
    }

    .preview-container {
        position: relative;
        display: inline-block;
    }

    .remove-image {
        position: absolute;
        top: -10px;
        right: -10px;
        background: var(--danger-color);
        color: white;
        border-radius: 50%;
        width: 30px;
        height: 30px;
        line-height: 30px;
        text-align: center;
        cursor: pointer;
        display: none;
        box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
        font-size: 1.2rem;
    }

    .remove-image:hover {
        background: #c82333;
        transform: scale(1.1);
    }

    .admin-links {
        background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
        border-radius: 12px;
        padding: 15px;
        margin-top: 25px;
        border-left: 4px solid var(--primary-color);
    }

    .admin-links a {
        color: var(--primary-color);
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .admin-links a:hover {
        color: var(--secondary-color);
        text-decoration: underline;
    }

    /* Mobile Responsive */
    @media (max-width: 768px) {
        .main-container {
            margin: 10px;
            border-radius: 15px;
        }

        .header-section {
            padding: 20px;
        }

        .header-section h1 {
            font-size: 2rem;
        }

        .form-section {
            padding: 25px;
        }

        .section-card {
            padding: 20px;
        }

        .form-control, .form-select {
            padding: 10px 12px;
        }

        .input-group .form-control {
            padding-left: 40px;
        }
    }

    @media (max-width: 480px) {
        .header-section h1 {
            font-size: 1.8rem;
        }

        .form-section {
            padding: 20px;
        }

        .section-card {
            padding: 15px;
        }
    }

    /* Animation */
    .section-card {
        animation: slideInUp 0.6s ease forwards;
        opacity: 0;
        transform: translateY(30px);
    }

    .section-card:nth-child(1) { animation-delay: 0.1s; }
    .section-card:nth-child(2) { animation-delay: 0.2s; }
    .section-card:nth-child(3) { animation-delay: 0.3s; }
    .section-card:nth-child(4) { animation-delay: 0.4s; }
    .section-card:nth-child(5) { animation-delay: 0.5s; }

    @keyframes slideInUp {
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    /* Loading state */
    .btn-submit.loading {
        pointer-events: none;
        opacity: 0.7;
    }

    .btn-submit.loading::after {
        content: '';
        position: absolute;
        width: 20px;
        height: 20px;
        top: 50%;
        left: 50%;
        margin-left: -10px;
        margin-top: -10px;
        border: 2px solid transparent;
        border-top: 2px solid white;
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
  </style>
</head>
<body>

<div class="main-container">
    <!-- Header Section -->
    <div class="header-section">
        <h1><i class="fas fa-file-alt me-3"></i>Token Slip Data Entry</h1>
        <p class="subtitle">Document Application Form</p>
    </div>

    <!-- Form Section -->
    <div class="form-section">
        <form action="token_data_handle.php" method="post" enctype="multipart/form-data" id="tokenForm">
            <!-- Basic Information Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-user-circle"></i>
                    Basic Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="tracking-id">
                                <i class="fas fa-barcode"></i>
                                Tracking ID
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-barcode"></i></span>
                                <input type="text" class="form-control" id="tracking-id" name="tracking-id" placeholder="Enter tracking ID">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="name">
                                <i class="fas fa-user"></i>
                                Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-user"></i></span>
                                <input type="text" class="form-control" id="name" name="name" placeholder="Enter name" required>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="cnic-number">
                                <i class="fas fa-id-card"></i>
                                CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="cnic-number" name="cnic-number" placeholder="Enter CNIC number" maxlength="15" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="application-type">
                                <i class="fas fa-file-alt"></i>
                                Application Type
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-file-alt"></i></span>
                                <select class="form-select" id="application-type" name="application-type" required>
                                    <option value="">Select Application Type</option>
                                    <?php foreach ($application_types as $app_type): ?>
                                        <option value="<?php echo htmlspecialchars($app_type['urdu_name']); ?>">
                                            <?php echo htmlspecialchars($app_type['english_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Additional Details Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-info-circle"></i>
                    Additional Details
                </h3>
                <div class="row">
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label" for="priority">
                                <i class="fas fa-star"></i>
                                Priority
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-star"></i></span>
                                <select class="form-select" id="priority" name="priority" required>
                                    <option value="">Select Priority</option>
                                    <?php foreach ($priorities as $priority): ?>
                                        <option value="<?php echo htmlspecialchars($priority['urdu_name']); ?>">
                                            <?php echo htmlspecialchars($priority['english_name']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label" for="date">
                                <i class="fas fa-calendar-alt"></i>
                                Date
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="date" class="form-control" id="date" name="date" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="form-group">
                            <label class="form-label" for="time">
                                <i class="fas fa-clock"></i>
                                Time
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-clock"></i></span>
                                <input type="time" class="form-control" id="time" name="time" required>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- Contact Information Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-address-book"></i>
                    Contact Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="mobile-no">
                                <i class="fas fa-phone"></i>
                                Mobile Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-phone"></i></span>
                                <input type="tel" class="form-control" id="mobile-no" name="mobile-no" placeholder="Enter mobile number" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="email-id">
                                <i class="fas fa-envelope"></i>
                                Email ID
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                <input type="email" class="form-control" id="email-id" name="email-id" placeholder="Enter email ID">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Family Information Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-users"></i>
                    Family Information
                </h3>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="father-name">
                                <i class="fas fa-male"></i>
                                Father Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-male"></i></span>
                                <input type="text" class="form-control" id="father-name" name="father-name" placeholder="Enter father name" required>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="father-cnic-no">
                                <i class="fas fa-id-card"></i>
                                Father CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="father-cnic-no" name="father-cnic-no" placeholder="Enter father CNIC number" maxlength="15">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="mother-name">
                                <i class="fas fa-female"></i>
                                Mother Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-female"></i></span>
                                <input type="text" class="form-control" id="mother-name" name="mother-name" placeholder="Enter mother name">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="mother-cnic-no">
                                <i class="fas fa-id-card"></i>
                                Mother CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="mother-cnic-no" name="mother-cnic-no" placeholder="Enter mother CNIC number" maxlength="15">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="spouse-name">
                                <i class="fas fa-heart"></i>
                                Spouse Name
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-heart"></i></span>
                                <input type="text" class="form-control" id="spouse-name" name="spouse-name" placeholder="Enter spouse name">
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="spouse-cnic-no">
                                <i class="fas fa-id-card"></i>
                                Spouse CNIC Number
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-id-card"></i></span>
                                <input type="text" class="form-control" id="spouse-cnic-no" name="spouse-cnic-no" placeholder="Enter spouse CNIC number" maxlength="15">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personal Details Section -->
            <div class="section-card">
                <h3 class="section-title">
                    <i class="fas fa-user-circle"></i>
                    Personal Details
                </h3>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="form-label" for="date-of-birth">
                                <i class="fas fa-calendar-alt"></i>
                                Date of Birth
                            </label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-calendar-alt"></i></span>
                                <input type="date" class="form-control" id="date-of-birth" name="date-of-birth">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="present-address">
                                <i class="fas fa-home"></i>
                                Present Address
                            </label>
                            <textarea class="form-control" id="present-address" name="present-address" rows="3" placeholder="Enter present address"></textarea>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="form-group">
                            <label class="form-label" for="permanent-address">
                                <i class="fas fa-building"></i>
                                Permanent Address
                            </label>
                            <textarea class="form-control" id="permanent-address" name="permanent-address" rows="3" placeholder="Enter permanent address"></textarea>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label class="form-label" for="picture">
                                <i class="fas fa-camera"></i>
                                Upload Picture
                            </label>
                            <input type="file" class="form-control" id="picture" name="picture" accept="image/*">
                            <div id="imagePreview">
                                <div class="preview-container">
                                    <img id="preview" src="#" alt="Preview">
                                    <div class="remove-image">&times;</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Submit Button -->
            <div class="form-group">
                <button type="submit" class="btn btn-submit" id="submitBtn">
                    <i class="fas fa-save me-2"></i>
                    Submit Application
                </button>
            </div>
        </form>

        <!-- Admin Links -->
        <div class="admin-links">
            <h5><i class="fas fa-cogs me-2"></i>Admin Panel</h5>
            <div class="d-flex flex-wrap gap-2">
                <a href="manage_lookups.php" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-cogs me-1"></i>Manage Application Types & Priorities
                </a>
                <a href="fee.php" class="btn btn-outline-success btn-sm">
                    <i class="fas fa-money-bill-wave me-1"></i>Manage Fees
                </a>
                <a href="setup_fee_table.php" class="btn btn-outline-secondary btn-sm">
                    <i class="fas fa-database me-1"></i>Setup Tables
                </a>
                <a href="show_data.php" class="btn btn-outline-info btn-sm">
                    <i class="fas fa-table me-1"></i>View Data
                </a>
            </div>
        </div>
    </div>
</div>

<!-- Bootstrap 5 JS -->
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
<!-- jQuery -->
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

<script>
$(document).ready(function() {
    // Add smooth animations
    const elements = document.querySelectorAll('.section-card');
    elements.forEach((el, index) => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(20px)';
        setTimeout(() => {
            el.style.transition = 'all 0.6s ease';
            el.style.opacity = '1';
            el.style.transform = 'translateY(0)';
        }, index * 100);
    });

    // Form validation
    $('#tokenForm').on('submit', function(e) {
        let isValid = true;
        const requiredFields = ['name', 'cnic-number', 'application-type', 'priority', 'date', 'time', 'mobile-no', 'father-name'];

        requiredFields.forEach(function(field) {
            const input = $(`#${field}`);
            if (!input.val().trim()) {
                input.addClass('is-invalid');
                isValid = false;
            } else {
                input.removeClass('is-invalid').addClass('is-valid');
            }
        });

        // Email validation
        const email = $('#email-id').val();
        if (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (!emailRegex.test(email)) {
                $('#email-id').addClass('is-invalid').removeClass('is-valid');
                isValid = false;
            } else {
                $('#email-id').removeClass('is-invalid').addClass('is-valid');
            }
        }

        // CNIC validation
        const cnicFields = ['cnic-number', 'father-cnic-no', 'mother-cnic-no', 'spouse-cnic-no'];
        cnicFields.forEach(function(field) {
            const cnic = $(`#${field}`).val();
            if (cnic) {
                const cnicRegex = /^\d{5}-\d{7}-\d{1}$/;
                if (!cnicRegex.test(cnic)) {
                    $(`#${field}`).addClass('is-invalid').removeClass('is-valid');
                    if (field === 'cnic-number') isValid = false; // Only main CNIC is required
                } else {
                    $(`#${field}`).removeClass('is-invalid').addClass('is-valid');
                }
            }
        });

        if (!isValid) {
            e.preventDefault();
            alert('Please fill in all required fields correctly.');
            $('.is-invalid').first().focus();
            return false;
        }

        // Show loading state
        const submitBtn = $('#submitBtn');
        submitBtn.addClass('loading');
        submitBtn.html('<i class="fas fa-spinner fa-spin me-2"></i>Submitting...');
    });

    // Real-time validation
    $('input[required], select[required]').on('blur', function() {
        if ($(this).val().trim()) {
            $(this).removeClass('is-invalid').addClass('is-valid');
        } else {
            $(this).addClass('is-invalid').removeClass('is-valid');
        }
    });

    // Email validation
    $('#email-id').on('blur', function() {
        const email = $(this).val();
        if (email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            if (emailRegex.test(email)) {
                $(this).removeClass('is-invalid').addClass('is-valid');
            } else {
                $(this).addClass('is-invalid').removeClass('is-valid');
            }
        }
    });

    // Phone number formatting
    $('#mobile-no').on('input', function() {
        let value = $(this).val().replace(/\D/g, '');
        if (value.length > 0 && !value.startsWith('92')) {
            if (value.startsWith('0')) {
                value = '92' + value.substring(1);
            } else {
                value = '92' + value;
            }
        }
        if (value.length > 2) {
            value = '+' + value.substring(0, 2) + ' ' + value.substring(2, 5) + ' ' + value.substring(5);
        }
        $(this).val(value);
    });

    // Set current date and time
    const now = new Date();
    const today = now.toISOString().split('T')[0];
    const currentTime = now.toTimeString().split(' ')[0].substring(0, 5);

    $('#date').val(today);
    $('#time').val(currentTime);
});

// CNIC input formatting
function formatCNIC(inputElement) {
    inputElement.addEventListener('input', function(event) {
        var value = event.target.value.replace(/[^0-9]/g, '');
        if (value.length > 5) {
            value = value.slice(0, 5) + '-' + value.slice(5);
        }
        if (value.length > 13) {
            value = value.slice(0, 13) + '-' + value.slice(13);
        }
        event.target.value = value;

        // Real-time validation
        const cnicRegex = /^\d{5}-\d{7}-\d{1}$/;
        if (value && cnicRegex.test(value)) {
            event.target.classList.remove('is-invalid');
            event.target.classList.add('is-valid');
        } else if (value) {
            event.target.classList.add('is-invalid');
            event.target.classList.remove('is-valid');
        }
    });
}

// Apply CNIC formatting to all CNIC fields
['cnic-number', 'father-cnic-no', 'mother-cnic-no', 'spouse-cnic-no'].forEach(function(id) {
    const element = document.getElementById(id);
    if (element) {
        formatCNIC(element);
    }
});

// Image Preview Functionality
document.getElementById('picture').addEventListener('change', function(e) {
    const file = e.target.files[0];
    const preview = document.getElementById('preview');
    const removeBtn = document.querySelector('.remove-image');

    if (file) {
        // Validate file type
        if (!file.type.startsWith('image/')) {
            alert('Please select a valid image file.');
            this.value = '';
            return;
        }

        // Validate file size (max 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('Image size should be less than 5MB.');
            this.value = '';
            return;
        }

        // Show preview
        const reader = new FileReader();
        reader.onload = function(e) {
            preview.src = e.target.result;
            preview.style.display = 'block';
            removeBtn.style.display = 'block';
        }
        reader.readAsDataURL(file);
    }
});

// Remove image preview
document.querySelector('.remove-image').addEventListener('click', function() {
    const preview = document.getElementById('preview');
    const fileInput = document.getElementById('picture');
    const removeBtn = document.querySelector('.remove-image');

    preview.src = '#';
    preview.style.display = 'none';
    removeBtn.style.display = 'none';
    fileInput.value = '';
});

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    if (e.ctrlKey && e.key === 's') {
        e.preventDefault();
        $('#tokenForm').submit();
    }
});

// Auto-save form data to localStorage
function autoSave() {
    const formData = {};
    $('#tokenForm input, #tokenForm select, #tokenForm textarea').each(function() {
        if (this.type !== 'file') {
            formData[this.id] = $(this).val();
        }
    });
    localStorage.setItem('tokenFormData', JSON.stringify(formData));
}

// Load saved form data
function loadSavedData() {
    const savedData = localStorage.getItem('tokenFormData');
    if (savedData) {
        const data = JSON.parse(savedData);
        Object.keys(data).forEach(key => {
            if (data[key]) {
                $(`#${key}`).val(data[key]);
            }
        });
    }
}

// Auto-save every 30 seconds
setInterval(autoSave, 30000);

// Load saved data when page loads
$(document).ready(function() {
    loadSavedData();
});

// Clear saved data on successful submission
$('#tokenForm').on('submit', function() {
    localStorage.removeItem('tokenFormData');
});
</script>

</body>
</html>
<?php include 'footer.php'; ?>
